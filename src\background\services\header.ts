// 请求头管理模块
// 处理请求头的设置和清理，使用Chrome declarativeNetRequest API

import type { Response } from "../../types/network"

export class HeaderManager {
  // 存储请求头规则的映射 (requestId -> ruleId)
  private requestHeaderRules = new Map<string, number>()
  // 存储扩展页面请求头规则的映射
  private extensionHeaderRules = new Map<string, number>()

  // 检测是否为HLS流
  private isHLSStream(url: string, type?: string): boolean {
    return url.includes('.m3u8') ||
      type === 'application/vnd.apple.mpegurl' ||
      type === 'application/x-mpegurl'
  }

  // 处理设置请求头请求（用于特定标签页）
  async handleSetRequestHeaders(
    payload: { url: string, requestHeaders?: chrome.webRequest.HttpHeader[], pageUrl?: string, tabId: number, pageTaskId?: string },
    sendResponse: (response: Response) => void
  ) {
    try {
      const { url, requestHeaders, pageUrl, tabId, pageTaskId } = payload

      if (!url || !tabId) {
        sendResponse({
          success: false,
          error: "缺少必要参数"
        })
        return
      }

      const ruleId = tabId

      // 准备请求头列表
      let headersToSet: chrome.webRequest.HttpHeader[] = []

      // 检测是否为 M3U8 流
      const isM3U8 = this.isHLSStream(url)

      // 如果有原始请求头，先过滤掉浏览器禁止修改的请求头
      if (requestHeaders && requestHeaders.length > 0) {
        const allowedHeaders = requestHeaders.filter((header: chrome.webRequest.HttpHeader) => {
          const name = header.name.toLowerCase()
          // 排除浏览器禁止修改的请求头
          const forbiddenHeaders = [
            'host', 'content-length', 'connection', 'upgrade',
            'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site'
          ]
          // 排除请求大小相关的请求头（避免部分下载，确保下载完整文件）
          const sizeRelatedHeaders = [
            'range', 'if-range', 'content-range', 'accept-ranges',
            'content-length', 'transfer-encoding', 'if-modified-since',
            'if-none-match', 'if-unmodified-since', 'if-match'
          ]
          return !forbiddenHeaders.includes(name) && !sizeRelatedHeaders.includes(name)
        })
        headersToSet = [...allowedHeaders]

        console.log(`过滤后的请求头数量: ${headersToSet.length}，原始数量: ${requestHeaders.length}`)
      }

      // 确保添加 Referer 请求头
      if (pageUrl) {
        const hasReferer = headersToSet.some(header =>
          header.name.toLowerCase() === 'referer'
        )

        if (!hasReferer) {
          headersToSet.push({
            name: 'Referer',
            value: pageUrl
          })
          console.log(`为标签页 ${tabId} 添加 Referer: ${pageUrl}`)
        }
      }

      // 为 M3U8 添加特殊的请求头
      if (isM3U8) {
        console.log(`检测到 M3U8 流，添加特殊请求头`)

        // 确保有 User-Agent
        const hasUserAgent = headersToSet.some(header =>
          header.name.toLowerCase() === 'user-agent'
        )

        if (!hasUserAgent) {
          headersToSet.push({
            name: 'User-Agent',
            value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          })
        }

        // 添加 Accept 头用于 HLS 流
        const hasAccept = headersToSet.some(header =>
          header.name.toLowerCase() === 'accept'
        )

        if (!hasAccept) {
          headersToSet.push({
            name: 'Accept',
            value: 'application/vnd.apple.mpegurl, application/x-mpegurl, application/mpegurl, video/mp2t, */*'
          })
        }

        // 确保有 Origin（如果有页面URL）
        if (pageUrl) {
          const hasOrigin = headersToSet.some(header =>
            header.name.toLowerCase() === 'origin'
          )

          if (!hasOrigin) {
            try {
              const pageUrlObj = new URL(pageUrl)
              headersToSet.push({
                name: 'Origin',
                value: `${pageUrlObj.protocol}//${pageUrlObj.host}`
              })
            } catch (e) {
              console.warn('无法解析页面 URL 生成 Origin:', e)
            }
          }
        }
      }

      if (headersToSet.length === 0) {
        console.log('没有可设置的请求头')
        sendResponse({ success: true })
        return
      }

      // 构建资源类型列表
      const resourceTypes: chrome.declarativeNetRequest.ResourceType[] = [
        chrome.declarativeNetRequest.ResourceType.MEDIA,
        chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST,
        chrome.declarativeNetRequest.ResourceType.OTHER
      ]

      // 构建更精确的URL匹配规则
      const escapedUrl = url.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')

      // 构建规则条件
      const condition: chrome.declarativeNetRequest.RuleCondition = {
        regexFilter: `^${escapedUrl}(\\?.*)?$`,
        resourceTypes
      }

      // 如果有tabId，添加到条件中（类似cat-catch的做法）
      if (tabId) {
        condition.tabIds = [tabId]
      }

      const rule: chrome.declarativeNetRequest.Rule = {
        id: ruleId,
        priority: isM3U8 ? 2 : 1,
        action: {
          type: chrome.declarativeNetRequest.RuleActionType.MODIFY_HEADERS,
          requestHeaders: headersToSet.map(header => ({
            header: header.name,
            operation: chrome.declarativeNetRequest.HeaderOperation.SET,
            value: header.value || ""
          }))
        },
        condition
      }

      // 先移除可能存在的旧规则，再添加新规则（使用SessionRules如cat-catch）
      await chrome.declarativeNetRequest.updateSessionRules({
        removeRuleIds: [ruleId],
        addRules: [rule]
      })

      // 存储规则ID映射
      this.requestHeaderRules.set(tabId.toString(), ruleId)

      console.log(`已设置${isM3U8 ? 'M3U8' : ''}请求头规则:`)
      console.log(`  标签页ID: ${tabId}`)
      console.log(`  规则ID: ${ruleId}`)
      console.log(`  目标URL: ${url}`)
      console.log(`  请求头数量: ${headersToSet.length}`)

      // 先回复给调用者
      sendResponse({
        success: true,
        message: "请求头规则已设置"
      })

      // 然后向特定标签页发送通知消息
      if (pageTaskId) {
        try {
          await chrome.tabs.sendMessage(tabId, {
            type: 'HEADERS_SET_COMPLETED',
            data: {
              success: true,
              message: "请求头规则已设置",
              pageTaskId: pageTaskId,
              url: url
            }
          })
          console.log(`已向标签页 ${tabId} 发送请求头设置完成通知`)
        } catch (error) {
          console.error(`向标签页 ${tabId} 发送通知失败:`, error)
        }
      }
    } catch (error) {
      console.error("设置请求头失败:", error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "设置请求头失败"
      })
    }
  }

  // 处理为扩展页面设置请求头（用于侧边栏和弹出窗口预览）
  async handleSetRequestHeadersForExtension(
    payload: { requestId: string, url: string, requestHeaders?: chrome.webRequest.HttpHeader[], pageUrl?: string },
    sendResponse: (response: Response) => void
  ) {
    try {
      const { requestId, url, requestHeaders, pageUrl } = payload

      if (!requestId || !url) {
        sendResponse({
          success: false,
          error: "缺少必要参数"
        })
        return
      }

      // 先移除可能存在的旧规则
      await chrome.declarativeNetRequest.updateSessionRules({ removeRuleIds: [1] })

      // 获取当前标签页信息
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      const currentTab = tabs[0]

      // 准备请求头列表 - 使用与 handleSetRequestHeaders 完全相同的逻辑
      let headersToSet: chrome.webRequest.HttpHeader[] = []

      // 检测是否为 M3U8 流
      const isM3U8 = this.isHLSStream(url)

      // 如果有原始请求头，先过滤掉浏览器禁止修改的请求头（与 handleSetRequestHeaders 相同）
      if (requestHeaders && requestHeaders.length > 0) {
        const allowedHeaders = requestHeaders.filter((header: chrome.webRequest.HttpHeader) => {
          const name = header.name.toLowerCase()
          // 排除浏览器禁止修改的请求头
          const forbiddenHeaders = [
            'host', 'content-length', 'connection', 'upgrade',
            'sec-fetch-dest', 'sec-fetch-mode', 'sec-fetch-site'
          ]
          // 排除请求大小相关的请求头（避免部分下载，确保下载完整文件）
          const sizeRelatedHeaders = [
            'range', 'if-range', 'content-range', 'accept-ranges',
            'content-length', 'transfer-encoding', 'if-modified-since',
            'if-none-match', 'if-unmodified-since', 'if-match'
          ]
          return !forbiddenHeaders.includes(name) && !sizeRelatedHeaders.includes(name)
        })
        headersToSet = [...allowedHeaders]

        console.log(`扩展页面过滤后的请求头数量: ${headersToSet.length}，原始数量: ${requestHeaders.length}`)
      }

      // 为扩展页面预览添加必要的请求头，确保与网页下载时的请求头一致
      const essentialHeaders = [
        // 添加缓存控制（模拟网页下载时的行为）
        { name: 'Cache-Control', value: 'max-age=0' },
        // 确保正确的 Accept 头
        { name: 'Accept', value: '*/*' }
      ]

      // 添加必要的请求头（如果不存在的话）
      for (const essentialHeader of essentialHeaders) {
        const exists = headersToSet.some(header =>
          header.name.toLowerCase() === essentialHeader.name.toLowerCase()
        )
        if (!exists) {
          headersToSet.push(essentialHeader)
        }
      }

      // 确保添加 Referer 请求头（与 handleSetRequestHeaders 相同）
      console.log(`扩展页面 pageUrl 参数: ${pageUrl || '未提供'}`)
      if (pageUrl) {
        const hasReferer = headersToSet.some(header =>
          header.name.toLowerCase() === 'referer'
        )

        if (!hasReferer) {
          headersToSet.push({
            name: 'Referer',
            value: pageUrl
          })
          console.log(`为扩展页面添加 Referer: ${pageUrl}`)
        } else {
          console.log(`扩展页面已存在 Referer 请求头`)
        }
      } else {
        console.log(`扩展页面缺少 pageUrl，无法设置 Referer`)
      }

      // 为 M3U8 添加特殊的请求头（与 handleSetRequestHeaders 相同）
      if (isM3U8) {
        console.log(`扩展页面检测到 M3U8 流，添加特殊请求头`)

        // 确保有 User-Agent
        const hasUserAgent = headersToSet.some(header =>
          header.name.toLowerCase() === 'user-agent'
        )

        if (!hasUserAgent) {
          headersToSet.push({
            name: 'User-Agent',
            value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          })
        }

        // 添加 Accept 头用于 HLS 流
        const hasAccept = headersToSet.some(header =>
          header.name.toLowerCase() === 'accept'
        )

        if (!hasAccept) {
          headersToSet.push({
            name: 'Accept',
            value: 'application/vnd.apple.mpegurl, application/x-mpegurl, application/mpegurl, video/mp2t, */*'
          })
        }

        console.log('pageUrl', pageUrl)

        // 确保有 Origin（如果有页面URL）
        if (pageUrl) {
          const hasOrigin = headersToSet.some(header =>
            header.name.toLowerCase() === 'origin'
          )

          if (!hasOrigin) {
            try {
              const pageUrlObj = new URL(pageUrl)
              headersToSet.push({
                name: 'Origin',
                value: `${pageUrlObj.protocol}//${pageUrlObj.host}`
              })
            } catch (e) {
              console.warn('扩展页面无法解析页面 URL 生成 Origin:', e)
            }
          }
        }
      }

      if (headersToSet.length === 0) {
        console.log('扩展页面没有可设置的请求头')
        sendResponse({ success: true })
        return
      }

      // 构建规则（完全参考猫抓插件的 setRequestHeaders 方法）
      // 使用固定的规则ID 1，类似猫抓插件
      const ruleId = 1
      const rules: chrome.declarativeNetRequest.UpdateRuleOptions = { removeRuleIds: [ruleId] }

      if (headersToSet.length > 0) {
        // 构建请求头修改规则
        const requestHeaderModifications = [
          // 首先移除可能导致部分下载的 Range 相关请求头
          { header: 'range', operation: chrome.declarativeNetRequest.HeaderOperation.REMOVE },
          { header: 'if-range', operation: chrome.declarativeNetRequest.HeaderOperation.REMOVE },
          // 然后设置我们需要的请求头
          ...headersToSet.map(header => ({
            header: header.name,
            operation: chrome.declarativeNetRequest.HeaderOperation.SET,
            value: header.value || ""
          }))
        ]

        const rule: chrome.declarativeNetRequest.Rule = {
          id: ruleId,
          priority: ruleId,
          action: {
            type: chrome.declarativeNetRequest.RuleActionType.MODIFY_HEADERS,
            requestHeaders: requestHeaderModifications
          },
          condition: {
            resourceTypes: [
              chrome.declarativeNetRequest.ResourceType.XMLHTTPREQUEST,
              chrome.declarativeNetRequest.ResourceType.MEDIA,
              chrome.declarativeNetRequest.ResourceType.IMAGE
            ]
          }
        }

        // 对于扩展页面，优先使用 tabIds（如果有当前标签页）
        if (currentTab && currentTab.id) {
          rule.condition.tabIds = [currentTab.id]
          console.log(`扩展页面使用 tabIds: [${currentTab.id}]`)
        } else {
          // 如果没有当前标签页，使用 initiatorDomains（完全参考猫抓的做法）
          // 获取扩展ID作为域名
          const extensionId = chrome.runtime.id
          rule.condition.initiatorDomains = [extensionId]
          console.log(`扩展页面使用 initiatorDomains: [${extensionId}]`)
        }

        rules.addRules = [rule]
      }

      await chrome.declarativeNetRequest.updateSessionRules(rules)

      // 存储规则ID映射
      this.extensionHeaderRules.set(requestId, ruleId)

      console.log(`已为扩展页面设置${isM3U8 ? 'M3U8' : ''}请求头规则:`)
      console.log(`  请求ID: ${requestId}`)
      console.log(`  规则ID: ${ruleId}`)
      console.log(`  目标URL: ${url}`)
      console.log(`  请求头数量: ${headersToSet.length}`)
      console.log(`  当前标签页: ${currentTab?.id || '无'}`)

      sendResponse({
        success: true,
        message: "扩展页面请求头规则已设置"
      })
    } catch (error) {
      console.error("为扩展页面设置请求头失败:", error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "为扩展页面设置请求头失败"
      })
    }
  }

  // 处理清理请求头请求
  async handleCleanupRequestHeaders(
    payload: { requestId?: string, tabId?: number },
    sendResponse: (response: Response) => void
  ) {
    try {
      const { requestId, tabId } = payload

      if (requestId) {
        // 清理扩展页面的请求头规则
        const ruleId = this.extensionHeaderRules.get(requestId)
        if (ruleId) {
          await chrome.declarativeNetRequest.updateSessionRules({
            removeRuleIds: [ruleId]
          })
          this.extensionHeaderRules.delete(requestId)
          console.log(`已清理扩展页面请求头规则，请求ID: ${requestId}, 规则ID: ${ruleId}`)
        }
      }

      if (tabId) {
        // 清理特定标签页的请求头规则
        const ruleId = this.requestHeaderRules.get(tabId.toString())
        if (ruleId) {
          await chrome.declarativeNetRequest.updateSessionRules({
            removeRuleIds: [ruleId]
          })
          this.requestHeaderRules.delete(tabId.toString())
          console.log(`已清理标签页请求头规则，标签页ID: ${tabId}, 规则ID: ${ruleId}`)
        }
      }

      sendResponse({
        success: true,
        message: "请求头规则已清理"
      })
    } catch (error) {
      console.error("清理请求头失败:", error)
      sendResponse({
        success: false,
        error: error instanceof Error ? error.message : "清理请求头失败"
      })
    }
  }
}
